/// خدمة التخطيط الوظيفي
/// توفر وظائف إدارة المسارات الوظيفية وخطط التطوير الوظيفي
library;

import 'dart:convert';
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class CareerPlanningService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدارة المسارات الوظيفية

  /// الحصول على جميع المسارات الوظيفية
  Future<List<CareerPath>> getAllCareerPaths({
    bool activeOnly = false,
    int? departmentId,
    String? searchQuery,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(1);
      }

      if (departmentId != null) {
        whereClause += ' AND department_id = ?';
        whereArgs.add(departmentId);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (path_name LIKE ? OR description LIKE ?)';
        final searchPattern = '%$searchQuery%';
        whereArgs.addAll([searchPattern, searchPattern]);
      }

      final result = await db.query(
        AppConstants.careerPathsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'path_name ASC',
      );

      return result.map((map) => CareerPath.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المسارات الوظيفية',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على مسار وظيفي بالمعرف
  Future<CareerPath?> getCareerPathById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.careerPathsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return CareerPath.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المسار الوظيفي',
        category: 'CareerPlanningService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء مسار وظيفي جديد
  Future<CareerPath> createCareerPath(CareerPath careerPath) async {
    try {
      // التحقق من صحة البيانات
      await _validateCareerPath(careerPath);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار الاسم
      final existingPath = await _getCareerPathByName(careerPath.pathName);
      if (existingPath != null) {
        throw ValidationException('اسم المسار الوظيفي موجود مسبقاً');
      }

      final now = DateTime.now();
      final pathData = careerPath.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.careerPathsTable,
        pathData.toMap(),
      );

      final newPath = pathData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'CareerPath',
        entityId: id,
        description: 'إنشاء مسار وظيفي جديد: ${careerPath.pathName}',
        newValues: newPath.toMap(),
      );

      LoggingService.info(
        'تم إنشاء مسار وظيفي جديد بنجاح',
        category: 'CareerPlanningService',
        data: {'id': id, 'name': careerPath.pathName},
      );

      return newPath;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المسار الوظيفي',
        category: 'CareerPlanningService',
        data: {'path': careerPath.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث مسار وظيفي
  Future<CareerPath> updateCareerPath(CareerPath careerPath) async {
    try {
      if (careerPath.id == null) {
        throw ValidationException('معرف المسار الوظيفي مطلوب للتحديث');
      }

      // التحقق من وجود المسار
      final existingPath = await getCareerPathById(careerPath.id!);
      if (existingPath == null) {
        throw ValidationException('المسار الوظيفي غير موجود');
      }

      // التحقق من صحة البيانات
      await _validateCareerPath(careerPath);

      final db = await _databaseHelper.database;

      final updatedPath = careerPath.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.careerPathsTable,
        updatedPath.toMap(),
        where: 'id = ?',
        whereArgs: [careerPath.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'CareerPath',
        entityId: careerPath.id!,
        description: 'تحديث مسار وظيفي: ${careerPath.pathName}',
        oldValues: existingPath.toMap(),
        newValues: updatedPath.toMap(),
      );

      LoggingService.info(
        'تم تحديث المسار الوظيفي بنجاح',
        category: 'CareerPlanningService',
        data: {'id': careerPath.id, 'name': careerPath.pathName},
      );

      return updatedPath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المسار الوظيفي',
        category: 'CareerPlanningService',
        data: {'path': careerPath.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف مسار وظيفي
  Future<void> deleteCareerPath(int id) async {
    try {
      final careerPath = await getCareerPathById(id);
      if (careerPath == null) {
        throw ValidationException('المسار الوظيفي غير موجود');
      }

      // التحقق من عدم وجود خطط تطوير مرتبطة
      final hasPlans = await _pathHasDevelopmentPlans(id);
      if (hasPlans) {
        throw ValidationException(
          'لا يمكن حذف المسار الوظيفي لوجود خطط تطوير مرتبطة به',
        );
      }

      final db = await _databaseHelper.database;
      await db.delete(
        AppConstants.careerPathsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'CareerPath',
        entityId: id,
        description: 'حذف مسار وظيفي: ${careerPath.pathName}',
        oldValues: careerPath.toMap(),
      );

      LoggingService.info(
        'تم حذف المسار الوظيفي بنجاح',
        category: 'CareerPlanningService',
        data: {'id': id, 'name': careerPath.pathName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف المسار الوظيفي',
        category: 'CareerPlanningService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إدارة خطط التطوير الوظيفي

  /// الحصول على جميع خطط التطوير
  Future<List<CareerDevelopmentPlan>> getAllDevelopmentPlans({
    int? employeeId,
    String? status,
    int? careerPathId,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (status != null && status.isNotEmpty) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (careerPathId != null) {
        whereClause += ' AND career_path_id = ?';
        whereArgs.add(careerPathId);
      }

      final result = await db.query(
        AppConstants.careerDevelopmentPlansTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => CareerDevelopmentPlan.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب خطط التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// للتوافق مع الشاشات الموجودة
  Future<List<CareerDevelopmentPlan>> getDevelopmentPlans() =>
      getAllDevelopmentPlans();

  /// الحصول على خطة تطوير بالمعرف
  Future<CareerDevelopmentPlan?> getDevelopmentPlanById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.careerDevelopmentPlansTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) {
        return null;
      }

      return CareerDevelopmentPlan.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب خطة التطوير',
        category: 'CareerPlanningService',
        data: {'error': e.toString(), 'id': id},
      );
      return null;
    }
  }

  /// إنشاء خطة تطوير وظيفي جديدة
  Future<CareerDevelopmentPlan> createDevelopmentPlan(
    CareerDevelopmentPlan plan,
  ) async {
    try {
      // التحقق من صحة البيانات
      await _validateDevelopmentPlan(plan);

      final db = await _databaseHelper.database;

      final now = DateTime.now();
      final planData = plan.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.careerDevelopmentPlansTable,
        planData.toMap(),
      );

      final newPlan = planData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'CareerDevelopmentPlan',
        entityId: id,
        description: 'إنشاء خطة تطوير وظيفي جديدة للموظف ${plan.employeeId}',
        newValues: newPlan.toMap(),
      );

      LoggingService.info(
        'تم إنشاء خطة تطوير وظيفي جديدة بنجاح',
        category: 'CareerPlanningService',
        data: {'id': id, 'employee_id': plan.employeeId},
      );

      return newPlan;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء خطة التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'plan': plan.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات التخطيط الوظيفي
  Future<Map<String, dynamic>> getCareerPlanningStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final totalPaths = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.careerPathsTable}',
      );

      final activePaths = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.careerPathsTable} WHERE is_active = 1',
      );

      final totalPlans = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.careerDevelopmentPlansTable}',
      );

      final activePlans = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.careerDevelopmentPlansTable} WHERE status = "active"',
      );

      final totalCount = totalPaths.first['count'] as int;
      final activeCount = activePaths.first['count'] as int;
      final plansCount = totalPlans.first['count'] as int;
      final activePlansCount = activePlans.first['count'] as int;

      return {
        'totalPaths': totalCount,
        'activePaths': activeCount,
        'inactivePaths': totalCount - activeCount,
        'totalPlans': plansCount,
        'activePlans': activePlansCount,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات التخطيط الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      return {
        'totalPaths': 0,
        'activePaths': 0,
        'inactivePaths': 0,
        'totalPlans': 0,
        'activePlans': 0,
      };
    }
  }

  /// دوال مساعدة خاصة

  /// التحقق من صحة بيانات المسار الوظيفي
  Future<void> _validateCareerPath(CareerPath careerPath) async {
    if (careerPath.pathName.trim().isEmpty) {
      throw ValidationException('اسم المسار الوظيفي مطلوب');
    }

    if (careerPath.pathName.length < 3) {
      throw ValidationException(
        'اسم المسار الوظيفي يجب أن يكون 3 أحرف على الأقل',
      );
    }
  }

  /// الحصول على مسار وظيفي بالاسم
  Future<CareerPath?> _getCareerPathByName(String name) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.careerPathsTable,
        where: 'path_name = ?',
        whereArgs: [name.trim()],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return CareerPath.fromMap(result.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// التحقق من وجود خطط تطوير للمسار
  Future<bool> _pathHasDevelopmentPlans(int pathId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.careerDevelopmentPlansTable,
        where: 'career_path_id = ?',
        whereArgs: [pathId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      return true; // في حالة الخطأ، نفترض وجود خطط لمنع الحذف
    }
  }

  /// التحقق من صحة بيانات خطة التطوير
  Future<void> _validateDevelopmentPlan(CareerDevelopmentPlan plan) async {
    if (plan.employeeId <= 0) {
      throw ValidationException('معرف الموظف مطلوب');
    }

    if (plan.targetDate != null && plan.targetDate!.isBefore(DateTime.now())) {
      throw ValidationException('التاريخ المستهدف لا يمكن أن يكون في الماضي');
    }
  }

  /// للتوافق مع الشاشات الموجودة - دوال إضافية

  /// جلب مراجعات التطوير الوظيفي
  Future<List<CareerReview>> getCareerReviews({
    int? employeeId,
    int? planId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (planId != null) {
        whereClause += ' AND plan_id = ?';
        whereArgs.add(planId);
      }

      if (fromDate != null) {
        whereClause += ' AND review_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND review_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final result = await db.query(
        AppConstants.careerReviewsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'review_date DESC',
      );

      return result.map((map) => CareerReview.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مراجعات التطوير الوظيفي',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء مسارات وظيفية افتراضية
  Future<void> createDefaultCareerPaths() async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // التحقق من وجود مسارات مسبقاً
      final existingPaths = await getAllCareerPaths();
      if (existingPaths.isNotEmpty) {
        LoggingService.info(
          'المسارات الوظيفية موجودة مسبقاً',
          category: 'CareerPlanningService',
        );
        return;
      }

      // إنشاء المسارات الافتراضية
      final defaultPaths = [
        {
          'path_name': 'المسار الإداري',
          'description': 'مسار وظيفي للمناصب الإدارية والقيادية',
          'levels': _createAdministrativeLevels(),
          'requirements': _createAdministrativeRequirements(),
        },
        {
          'path_name': 'المسار التقني',
          'description': 'مسار وظيفي للمناصب التقنية والفنية',
          'levels': _createTechnicalLevels(),
          'requirements': _createTechnicalRequirements(),
        },
        {
          'path_name': 'المسار المالي',
          'description': 'مسار وظيفي للمناصب المالية والمحاسبية',
          'levels': _createFinancialLevels(),
          'requirements': _createFinancialRequirements(),
        },
        {
          'path_name': 'مسار المبيعات',
          'description': 'مسار وظيفي لفريق المبيعات والتسويق',
          'levels': _createSalesLevels(),
          'requirements': _createSalesRequirements(),
        },
      ];

      for (final pathData in defaultPaths) {
        await db.insert(AppConstants.careerPathsTable, {
          ...pathData,
          'is_active': 1,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        });
      }

      LoggingService.info(
        'تم إنشاء ${defaultPaths.length} مسار وظيفي افتراضي',
        category: 'CareerPlanningService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المسارات الوظيفية الافتراضية',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث تقدم خطة التطوير
  Future<CareerDevelopmentPlan> updatePlanProgress({
    required int planId,
    required double progressPercentage,
    String? employeeNotes,
    String? managerNotes,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (progressPercentage < 0 || progressPercentage > 100) {
        throw ValidationException('نسبة التقدم يجب أن تكون بين 0 و 100');
      }

      // الحصول على الخطة الحالية
      final currentPlan = await getDevelopmentPlanById(planId);
      if (currentPlan == null) {
        throw ValidationException('خطة التطوير غير موجودة');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // تحديث ملاحظات التقدم
      String updatedNotes = currentPlan.progressNotes ?? '';
      if (employeeNotes != null || managerNotes != null) {
        final newNote =
            '''
تحديث ${now.day}/${now.month}/${now.year}:
${employeeNotes != null ? 'ملاحظات الموظف: $employeeNotes' : ''}
${managerNotes != null ? 'ملاحظات المدير: $managerNotes' : ''}
---
$updatedNotes''';
        updatedNotes = newNote;
      }

      // تحديث حالة الخطة بناءً على التقدم
      String newStatus = currentPlan.status;
      if (progressPercentage >= 100) {
        newStatus = 'completed';
      } else if (progressPercentage > 0) {
        newStatus = 'in_progress';
      }

      // تحديث الخطة في قاعدة البيانات
      await db.update(
        AppConstants.careerDevelopmentPlansTable,
        {
          'progress_notes': updatedNotes,
          'status': newStatus,
          'updated_at': now.toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [planId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'CareerDevelopmentPlan',
        entityId: planId,
        description: 'تحديث تقدم خطة التطوير',
        oldValues: currentPlan.toMap(),
        newValues: {
          'progressPercentage': progressPercentage,
          'status': newStatus,
          'employeeNotes': employeeNotes,
          'managerNotes': managerNotes,
        },
      );

      LoggingService.info(
        'تم تحديث تقدم خطة التطوير',
        category: 'CareerPlanningService',
        data: {
          'planId': planId,
          'progressPercentage': progressPercentage,
          'newStatus': newStatus,
        },
      );

      // إرجاع الخطة المحدثة
      return (await getDevelopmentPlanById(planId))!;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث تقدم خطة التطوير',
        category: 'CareerPlanningService',
        data: {'error': e.toString(), 'planId': planId},
      );
      rethrow;
    }
  }

  /// دوال مساعدة لإنشاء المسارات الافتراضية

  String _createAdministrativeLevels() {
    final levels = [
      {
        'level': 1,
        'title': 'موظف إداري',
        'requirements': ['شهادة جامعية'],
      },
      {
        'level': 2,
        'title': 'موظف إداري أول',
        'requirements': ['خبرة 3 سنوات'],
      },
      {
        'level': 3,
        'title': 'مشرف',
        'requirements': ['خبرة 5 سنوات', 'دورة إدارية'],
      },
      {
        'level': 4,
        'title': 'مدير قسم',
        'requirements': ['خبرة 8 سنوات', 'شهادة إدارة'],
      },
      {
        'level': 5,
        'title': 'مدير عام',
        'requirements': ['خبرة 12 سنة', 'ماجستير إدارة'],
      },
    ];
    return _encodeJson(levels);
  }

  String _createTechnicalLevels() {
    final levels = [
      {
        'level': 1,
        'title': 'فني مبتدئ',
        'requirements': ['دبلوم تقني'],
      },
      {
        'level': 2,
        'title': 'فني',
        'requirements': ['خبرة سنتين'],
      },
      {
        'level': 3,
        'title': 'فني أول',
        'requirements': ['خبرة 4 سنوات'],
      },
      {
        'level': 4,
        'title': 'مشرف تقني',
        'requirements': ['خبرة 7 سنوات'],
      },
      {
        'level': 5,
        'title': 'مدير تقني',
        'requirements': ['خبرة 10 سنوات'],
      },
    ];
    return _encodeJson(levels);
  }

  String _createFinancialLevels() {
    final levels = [
      {
        'level': 1,
        'title': 'محاسب مبتدئ',
        'requirements': ['شهادة محاسبة'],
      },
      {
        'level': 2,
        'title': 'محاسب',
        'requirements': ['خبرة 3 سنوات'],
      },
      {
        'level': 3,
        'title': 'محاسب أول',
        'requirements': ['خبرة 5 سنوات'],
      },
      {
        'level': 4,
        'title': 'مدير مالي',
        'requirements': ['خبرة 8 سنوات', 'CPA'],
      },
      {
        'level': 5,
        'title': 'مدير مالي عام',
        'requirements': ['خبرة 12 سنة'],
      },
    ];
    return _encodeJson(levels);
  }

  String _createSalesLevels() {
    final levels = [
      {
        'level': 1,
        'title': 'مندوب مبيعات',
        'requirements': ['مهارات تواصل'],
      },
      {
        'level': 2,
        'title': 'مندوب مبيعات أول',
        'requirements': ['خبرة سنتين'],
      },
      {
        'level': 3,
        'title': 'مشرف مبيعات',
        'requirements': ['خبرة 4 سنوات'],
      },
      {
        'level': 4,
        'title': 'مدير مبيعات',
        'requirements': ['خبرة 7 سنوات'],
      },
      {
        'level': 5,
        'title': 'مدير مبيعات عام',
        'requirements': ['خبرة 10 سنوات'],
      },
    ];
    return _encodeJson(levels);
  }

  String _createAdministrativeRequirements() {
    final requirements = [
      'شهادة جامعية في الإدارة أو ما يعادلها',
      'مهارات قيادية وإدارية',
      'إتقان الحاسوب والبرامج المكتبية',
      'مهارات تواصل ممتازة',
      'القدرة على العمل تحت الضغط',
    ];
    return _encodeJson(requirements);
  }

  String _createTechnicalRequirements() {
    final requirements = [
      'شهادة تقنية أو هندسية',
      'خبرة في المجال التقني',
      'إتقان الأدوات والبرامج التقنية',
      'القدرة على حل المشاكل',
      'مواكبة التطورات التقنية',
    ];
    return _encodeJson(requirements);
  }

  String _createFinancialRequirements() {
    final requirements = [
      'شهادة في المحاسبة أو المالية',
      'إتقان البرامج المحاسبية',
      'معرفة بالقوانين المالية',
      'دقة في العمل والتفاصيل',
      'مهارات تحليلية قوية',
    ];
    return _encodeJson(requirements);
  }

  String _createSalesRequirements() {
    final requirements = [
      'مهارات مبيعات وتسويق',
      'مهارات تواصل ممتازة',
      'القدرة على الإقناع',
      'معرفة بالسوق والمنافسين',
      'مهارات تفاوض',
    ];
    return _encodeJson(requirements);
  }

  String _encodeJson(dynamic data) {
    try {
      return jsonEncode(data);
    } catch (e) {
      return '[]';
    }
  }

  /// إنشاء مراجعة تقدم
  Future<void> _createProgressReview({
    required int planId,
    required int employeeId,
    required double progressPercentage,
    String? employeeNotes,
    String? managerNotes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final review = CareerReview(
        employeeId: employeeId,
        planId: planId,
        reviewDate: now,
        reviewType: 'progress',
        progressPercentage: progressPercentage,
        employeeNotes: employeeNotes,
        managerNotes: managerNotes,
        createdAt: now,
        updatedAt: now,
      );

      await db.insert(AppConstants.careerReviewsTable, review.toMap());

      LoggingService.info(
        'تم إنشاء مراجعة تقدم جديدة',
        category: 'CareerPlanningService',
        data: {
          'planId': planId,
          'employeeId': employeeId,
          'progressPercentage': progressPercentage,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء مراجعة التقدم',
        category: 'CareerPlanningService',
        data: {'error': e.toString()},
      );
      // لا نرمي الخطأ هنا لأن هذا ليس أساسياً
    }
  }
}
