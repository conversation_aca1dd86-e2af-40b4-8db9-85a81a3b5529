/// خدمة التدريب وتطوير الموظفين
/// توفر جميع العمليات المتعلقة بإدارة التدريب والتطوير المهني
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';

class TrainingService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إدارة البرامج التدريبية

  /// الحصول على جميع البرامج التدريبية
  Future<List<TrainingProgram>> getAllPrograms({
    bool activeOnly = false,
    String? category,
    String? searchQuery,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(1);
      }

      if (category != null && category.isNotEmpty) {
        whereClause += ' AND category = ?';
        whereArgs.add(category);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (name LIKE ? OR description LIKE ?)';
        final searchPattern = '%$searchQuery%';
        whereArgs.addAll([searchPattern, searchPattern]);
      }

      final result = await db.query(
        AppConstants.trainingProgramsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'name ASC',
      );

      return result.map((map) => TrainingProgram.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب البرامج التدريبية',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// للتوافق مع الشاشات الموجودة
  Future<List<TrainingProgram>> getAllTrainingPrograms() => getAllPrograms();

  /// الحصول على برنامج تدريبي بالمعرف
  Future<TrainingProgram?> getProgramById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.trainingProgramsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return TrainingProgram.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب البرنامج التدريبي',
        category: 'TrainingService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء برنامج تدريبي جديد
  Future<TrainingProgram> createProgram(TrainingProgram program) async {
    try {
      // التحقق من صحة البيانات
      await _validateProgram(program);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار الاسم
      final existingProgram = await _getProgramByName(program.name);
      if (existingProgram != null) {
        throw ValidationException('اسم البرنامج موجود مسبقاً');
      }

      final now = DateTime.now();
      final programData = program.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.trainingProgramsTable,
        programData.toMap(),
      );

      final newProgram = programData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'TrainingProgram',
        entityId: id,
        description: 'إنشاء برنامج تدريبي جديد: ${program.name}',
        newValues: newProgram.toMap(),
      );

      LoggingService.info(
        'تم إنشاء برنامج تدريبي جديد بنجاح',
        category: 'TrainingService',
        data: {'id': id, 'name': program.name},
      );

      return newProgram;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء البرنامج التدريبي',
        category: 'TrainingService',
        data: {'program': program.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// للتوافق مع الشاشات الموجودة
  Future<TrainingProgram> addTrainingProgram(TrainingProgram program) =>
      createProgram(program);

  /// تحديث برنامج تدريبي
  Future<TrainingProgram> updateProgram(TrainingProgram program) async {
    try {
      if (program.id == null) {
        throw ValidationException('معرف البرنامج مطلوب للتحديث');
      }

      // التحقق من وجود البرنامج
      final existingProgram = await getProgramById(program.id!);
      if (existingProgram == null) {
        throw ValidationException('البرنامج غير موجود');
      }

      // التحقق من صحة البيانات
      await _validateProgram(program);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار الاسم (باستثناء البرنامج الحالي)
      final duplicateProgram = await _getProgramByName(program.name);
      if (duplicateProgram != null && duplicateProgram.id != program.id) {
        throw ValidationException('اسم البرنامج موجود مسبقاً');
      }

      final updatedProgram = program.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.trainingProgramsTable,
        updatedProgram.toMap(),
        where: 'id = ?',
        whereArgs: [program.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'TrainingProgram',
        entityId: program.id!,
        description: 'تحديث برنامج تدريبي: ${program.name}',
        oldValues: existingProgram.toMap(),
        newValues: updatedProgram.toMap(),
      );

      LoggingService.info(
        'تم تحديث البرنامج التدريبي بنجاح',
        category: 'TrainingService',
        data: {'id': program.id, 'name': program.name},
      );

      return updatedProgram;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث البرنامج التدريبي',
        category: 'TrainingService',
        data: {'program': program.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف برنامج تدريبي
  Future<void> deleteProgram(int id) async {
    try {
      final program = await getProgramById(id);
      if (program == null) {
        throw ValidationException('البرنامج غير موجود');
      }

      // التحقق من عدم وجود جلسات مرتبطة
      final hasSessions = await _programHasSessions(id);
      if (hasSessions) {
        throw ValidationException(
          'لا يمكن حذف البرنامج لوجود جلسات تدريبية مرتبطة به',
        );
      }

      final db = await _databaseHelper.database;
      await db.delete(
        AppConstants.trainingProgramsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'TrainingProgram',
        entityId: id,
        description: 'حذف برنامج تدريبي: ${program.name}',
        oldValues: program.toMap(),
      );

      LoggingService.info(
        'تم حذف البرنامج التدريبي بنجاح',
        category: 'TrainingService',
        data: {'id': id, 'name': program.name},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف البرنامج التدريبي',
        category: 'TrainingService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات التدريب
  Future<Map<String, dynamic>> getTrainingStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final totalPrograms = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.trainingProgramsTable}',
      );

      final activePrograms = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.trainingProgramsTable} WHERE is_active = 1',
      );

      final totalSessions = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.trainingSessionsTable}',
      );

      final totalEnrollments = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.trainingEnrollmentsTable}',
      );

      final completedEnrollments = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.trainingEnrollmentsTable} WHERE status = "completed"',
      );

      final totalCount = totalPrograms.first['count'] as int;
      final activeCount = activePrograms.first['count'] as int;
      final sessionsCount = totalSessions.first['count'] as int;
      final enrollmentsCount = totalEnrollments.first['count'] as int;
      final completedCount = completedEnrollments.first['count'] as int;

      final completionRate = enrollmentsCount > 0
          ? (completedCount / enrollmentsCount * 100).toStringAsFixed(1)
          : '0.0';

      return {
        'totalPrograms': totalCount,
        'activePrograms': activeCount,
        'inactivePrograms': totalCount - activeCount,
        'totalSessions': sessionsCount,
        'totalEnrollments': enrollmentsCount,
        'completedEnrollments': completedCount,
        'completionRate': completionRate,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return {
        'totalPrograms': 0,
        'activePrograms': 0,
        'inactivePrograms': 0,
        'totalSessions': 0,
        'totalEnrollments': 0,
        'completedEnrollments': 0,
        'completionRate': '0.0',
      };
    }
  }

  /// إدارة الجلسات التدريبية

  /// الحصول على جميع الجلسات التدريبية
  Future<List<TrainingSession>> getAllSessions({
    int? programId,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (programId != null) {
        whereClause += ' AND program_id = ?';
        whereArgs.add(programId);
      }

      if (status != null && status.isNotEmpty) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (startDate != null) {
        whereClause += ' AND start_date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += ' AND end_date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      final result = await db.query(
        AppConstants.trainingSessionsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'start_date DESC',
      );

      return result.map((map) => TrainingSession.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الجلسات التدريبية',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// دوال مساعدة خاصة

  /// التحقق من صحة بيانات البرنامج
  Future<void> _validateProgram(TrainingProgram program) async {
    if (program.name.trim().isEmpty) {
      throw ValidationException('اسم البرنامج مطلوب');
    }

    if (program.name.length < 3) {
      throw ValidationException('اسم البرنامج يجب أن يكون 3 أحرف على الأقل');
    }

    if (program.durationHours < 0) {
      throw ValidationException('مدة البرنامج لا يمكن أن تكون سالبة');
    }

    if (program.cost < 0) {
      throw ValidationException('تكلفة البرنامج لا يمكن أن تكون سالبة');
    }
  }

  /// الحصول على برنامج بالاسم
  Future<TrainingProgram?> _getProgramByName(String name) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.trainingProgramsTable,
        where: 'name = ?',
        whereArgs: [name.trim()],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return TrainingProgram.fromMap(result.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// التحقق من وجود جلسات للبرنامج
  Future<bool> _programHasSessions(int programId) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.trainingSessionsTable,
        where: 'program_id = ?',
        whereArgs: [programId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      return true; // في حالة الخطأ، نفترض وجود جلسات لمنع الحذف
    }
  }

  /// تسجيل موظف في برنامج تدريبي
  Future<TrainingEnrollment> enrollEmployee({
    required int employeeId,
    required int programId,
    DateTime? enrollmentDate,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // التحقق من وجود البرنامج
      final program = await getProgramById(programId);
      if (program == null) {
        throw ValidationException('البرنامج التدريبي غير موجود');
      }

      // التحقق من عدم التسجيل المسبق
      final existingEnrollment = await _getExistingEnrollment(
        employeeId,
        programId,
      );
      if (existingEnrollment != null) {
        throw ValidationException('الموظف مسجل مسبقاً في هذا البرنامج');
      }

      final enrollment = TrainingEnrollment(
        employeeId: employeeId,
        sessionId: programId, // استخدام programId كـ sessionId مؤقتاً
        enrollmentDate: enrollmentDate ?? now,
        status: 'enrolled',
        feedback: notes,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(
        AppConstants.trainingEnrollmentsTable,
        enrollment.toMap(),
      );

      final newEnrollment = enrollment.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'TrainingEnrollment',
        entityId: id,
        description: 'تسجيل موظف في برنامج تدريبي',
        newValues: newEnrollment.toMap(),
      );

      LoggingService.info(
        'تم تسجيل موظف في برنامج تدريبي',
        category: 'TrainingService',
        data: {
          'enrollmentId': id,
          'employeeId': employeeId,
          'programId': programId,
        },
      );

      return newEnrollment;
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الموظف في البرنامج التدريبي',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تتبع تقدم الموظف في التدريب
  Future<void> trackProgress({
    required int enrollmentId,
    required double progressPercentage,
    String? notes,
    bool? isCompleted,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // التحقق من صحة البيانات
      if (progressPercentage < 0 || progressPercentage > 100) {
        throw ValidationException('نسبة التقدم يجب أن تكون بين 0 و 100');
      }

      // الحصول على التسجيل الحالي
      final currentEnrollment = await _getEnrollmentById(enrollmentId);
      if (currentEnrollment == null) {
        throw ValidationException('تسجيل التدريب غير موجود');
      }

      // تحديث حالة التسجيل
      String newStatus = currentEnrollment.status;
      DateTime? completionDate;

      if (isCompleted == true || progressPercentage >= 100) {
        newStatus = 'completed';
        completionDate = now;
      } else if (progressPercentage > 0) {
        newStatus = 'in_progress';
      }

      // تحديث ملاحظات التقدم
      String updatedFeedback = currentEnrollment.feedback ?? '';
      if (notes != null) {
        final newNote =
            '''
تحديث ${now.day}/${now.month}/${now.year}:
التقدم: ${progressPercentage.toStringAsFixed(1)}%
$notes
---
$updatedFeedback''';
        updatedFeedback = newNote;
      }

      // تحديث التسجيل في قاعدة البيانات
      await db.update(
        AppConstants.trainingEnrollmentsTable,
        {
          'progress_percentage': progressPercentage,
          'status': newStatus,
          'completion_date': completionDate?.toIso8601String(),
          'feedback': updatedFeedback,
          'updated_at': now.toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [enrollmentId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'TrainingEnrollment',
        entityId: enrollmentId,
        description: 'تحديث تقدم التدريب',
        oldValues: currentEnrollment.toMap(),
        newValues: {
          'progressPercentage': progressPercentage,
          'status': newStatus,
          'notes': notes,
        },
      );

      LoggingService.info(
        'تم تحديث تقدم التدريب',
        category: 'TrainingService',
        data: {
          'enrollmentId': enrollmentId,
          'progressPercentage': progressPercentage,
          'newStatus': newStatus,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تتبع تقدم التدريب',
        category: 'TrainingService',
        data: {'error': e.toString(), 'enrollmentId': enrollmentId},
      );
      rethrow;
    }
  }

  /// إنشاء برامج تدريبية افتراضية
  Future<void> createDefaultPrograms() async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();

      // التحقق من وجود برامج مسبقاً
      final existingPrograms = await getAllPrograms();
      if (existingPrograms.isNotEmpty) {
        LoggingService.info(
          'البرامج التدريبية موجودة مسبقاً',
          category: 'TrainingService',
        );
        return;
      }

      // إنشاء البرامج الافتراضية
      final defaultPrograms = [
        {
          'name': 'مهارات الحاسوب الأساسية',
          'description': 'تعلم أساسيات استخدام الحاسوب والبرامج المكتبية',
          'category': 'تقني',
          'level': 'مبتدئ',
          'duration_hours': 40,
          'cost': 500.0,
          'delivery_method': 'حضوري',
          'max_participants': 20,
          'is_active': 1,
        },
        {
          'name': 'مهارات التواصل الفعال',
          'description': 'تطوير مهارات التواصل والعرض والتقديم',
          'category': 'مهارات شخصية',
          'level': 'متوسط',
          'duration_hours': 24,
          'cost': 300.0,
          'delivery_method': 'حضوري',
          'max_participants': 15,
          'is_active': 1,
        },
        {
          'name': 'إدارة الوقت والأولويات',
          'description': 'تعلم تقنيات إدارة الوقت وترتيب الأولويات',
          'category': 'إدارية',
          'level': 'متوسط',
          'duration_hours': 16,
          'cost': 200.0,
          'delivery_method': 'أونلاين',
          'max_participants': 30,
          'is_active': 1,
        },
        {
          'name': 'القيادة والإدارة',
          'description': 'تطوير مهارات القيادة وإدارة الفرق',
          'category': 'إدارية',
          'level': 'متقدم',
          'duration_hours': 32,
          'cost': 800.0,
          'delivery_method': 'مختلط',
          'max_participants': 12,
          'is_active': 1,
        },
      ];

      for (final programData in defaultPrograms) {
        await db.insert(AppConstants.trainingProgramsTable, {
          ...programData,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        });
      }

      LoggingService.info(
        'تم إنشاء ${defaultPrograms.length} برنامج تدريبي افتراضي',
        category: 'TrainingService',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء البرامج التدريبية الافتراضية',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إنشاء تقرير التدريب
  Future<Map<String, dynamic>> generateTrainingReport({
    int? employeeId,
    int? programId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND te.employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (programId != null) {
        whereClause += ' AND te.program_id = ?';
        whereArgs.add(programId);
      }

      if (fromDate != null) {
        whereClause += ' AND te.enrollment_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND te.enrollment_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      // إحصائيات عامة
      final statsResult = await db.rawQuery('''
        SELECT
          COUNT(*) as total_enrollments,
          COUNT(CASE WHEN te.status = 'completed' THEN 1 END) as completed_enrollments,
          COUNT(CASE WHEN te.status = 'in_progress' THEN 1 END) as in_progress_enrollments,
          COUNT(CASE WHEN te.status = 'enrolled' THEN 1 END) as enrolled_enrollments,
          AVG(te.progress_percentage) as average_progress
        FROM ${AppConstants.trainingEnrollmentsTable} te
        WHERE $whereClause
      ''', whereArgs);

      final stats = statsResult.first;

      return {
        'period': {
          'from': fromDate?.toIso8601String().split('T')[0],
          'to': toDate?.toIso8601String().split('T')[0],
        },
        'statistics': {
          'totalEnrollments': stats['total_enrollments'] ?? 0,
          'completedEnrollments': stats['completed_enrollments'] ?? 0,
          'inProgressEnrollments': stats['in_progress_enrollments'] ?? 0,
          'enrolledEnrollments': stats['enrolled_enrollments'] ?? 0,
          'averageProgress': stats['average_progress'] ?? 0.0,
        },
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء تقرير التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// دوال مساعدة

  Future<TrainingEnrollment?> _getExistingEnrollment(
    int employeeId,
    int sessionId,
  ) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.trainingEnrollmentsTable,
        where: 'employee_id = ? AND session_id = ?',
        whereArgs: [employeeId, sessionId],
      );

      if (result.isNotEmpty) {
        return TrainingEnrollment.fromMap(result.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<TrainingSession?> _getSessionById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.trainingSessionsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return TrainingSession.fromMap(result.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<TrainingEnrollment?> _getEnrollmentById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.trainingEnrollmentsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return TrainingEnrollment.fromMap(result.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
