/// خدمة إدارة الوثائق
/// توفر وظائف شاملة لإدارة وثائق الموظفين ورفع الملفات
library;

import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class DocumentService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // مجلد حفظ الوثائق
  static const String _documentsFolder = 'documents';
  static const String _employeeDocumentsFolder = 'employee_documents';

  /// الحصول على جميع وثائق موظف
  Future<List<Document>> getEmployeeDocuments({
    required int employeeId,
    String? documentType,
    bool? isConfidential,
    bool includeExpired = true,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'employee_id = ?';
      List<dynamic> whereArgs = [employeeId];

      if (documentType != null && documentType.isNotEmpty) {
        whereClause += ' AND document_type = ?';
        whereArgs.add(documentType);
      }

      if (isConfidential != null) {
        whereClause += ' AND is_confidential = ?';
        whereArgs.add(isConfidential ? 1 : 0);
      }

      if (!includeExpired) {
        whereClause += ' AND (expiry_date IS NULL OR expiry_date > ?)';
        whereArgs.add(DateTime.now().toIso8601String().split('T')[0]);
      }

      final result = await db.query(
        AppConstants.documentsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => Document.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب وثائق الموظف',
        category: 'DocumentService',
        data: {'error': e.toString(), 'employeeId': employeeId},
      );
      return [];
    }
  }

  /// الحصول على وثيقة بالمعرف
  Future<Document?> getDocumentById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.documentsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return Document.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الوثيقة',
        category: 'DocumentService',
        data: {'error': e.toString(), 'id': id},
      );
      return null;
    }
  }

  /// رفع وثيقة جديدة
  Future<Document> uploadDocument({
    required int employeeId,
    required String documentType,
    required String documentName,
    required Uint8List fileData,
    String? mimeType,
    String? description,
    bool isConfidential = false,
    DateTime? expiryDate,
    int? uploadedBy,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateDocument(
        employeeId: employeeId,
        documentType: documentType,
        documentName: documentName,
        fileData: fileData,
      );

      // إنشاء مجلد الوثائق إذا لم يكن موجوداً
      final documentsDir = await _createDocumentsDirectory(employeeId);

      // إنشاء اسم ملف فريد
      final fileName = _generateUniqueFileName(documentName);
      final filePath = path.join(documentsDir.path, fileName);

      // حفظ الملف
      final file = File(filePath);
      await file.writeAsBytes(fileData);

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final document = Document(
        employeeId: employeeId,
        documentType: documentType,
        documentName: documentName,
        filePath: filePath,
        fileSize: fileData.length,
        mimeType: mimeType,
        description: description,
        isConfidential: isConfidential,
        expiryDate: expiryDate,
        uploadedBy: uploadedBy,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(AppConstants.documentsTable, document.toMap());

      final newDocument = document.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPLOAD',
        entityType: 'Document',
        entityId: id,
        description: 'رفع وثيقة جديدة: $documentName',
        newValues: newDocument.toMap(),
      );

      LoggingService.info(
        'تم رفع وثيقة جديدة',
        category: 'DocumentService',
        data: {
          'documentId': id,
          'employeeId': employeeId,
          'documentType': documentType,
          'fileSize': fileData.length,
        },
      );

      return newDocument;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفع الوثيقة',
        category: 'DocumentService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث معلومات وثيقة
  Future<Document> updateDocument({
    required int documentId,
    String? documentName,
    String? description,
    bool? isConfidential,
    DateTime? expiryDate,
    int? updatedBy,
  }) async {
    try {
      final existingDocument = await getDocumentById(documentId);
      if (existingDocument == null) {
        throw ValidationException('الوثيقة غير موجودة');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedDocument = existingDocument.copyWith(
        documentName: documentName ?? existingDocument.documentName,
        description: description ?? existingDocument.description,
        isConfidential: isConfidential ?? existingDocument.isConfidential,
        expiryDate: expiryDate ?? existingDocument.expiryDate,
        uploadedBy: updatedBy ?? existingDocument.uploadedBy,
        updatedAt: now,
      );

      await db.update(
        AppConstants.documentsTable,
        updatedDocument.toMap(),
        where: 'id = ?',
        whereArgs: [documentId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Document',
        entityId: documentId,
        description: 'تحديث الوثيقة: ${updatedDocument.documentName}',
        oldValues: existingDocument.toMap(),
        newValues: updatedDocument.toMap(),
      );

      LoggingService.info(
        'تم تحديث الوثيقة',
        category: 'DocumentService',
        data: {'documentId': documentId},
      );

      return updatedDocument;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث الوثيقة',
        category: 'DocumentService',
        data: {'error': e.toString(), 'documentId': documentId},
      );
      rethrow;
    }
  }

  /// حذف وثيقة
  Future<void> deleteDocument(int documentId) async {
    try {
      final document = await getDocumentById(documentId);
      if (document == null) {
        throw ValidationException('الوثيقة غير موجودة');
      }

      // حذف الملف من النظام
      final file = File(document.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      final db = await _databaseHelper.database;

      await db.delete(
        AppConstants.documentsTable,
        where: 'id = ?',
        whereArgs: [documentId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'Document',
        entityId: documentId,
        description: 'حذف الوثيقة: ${document.documentName}',
        oldValues: document.toMap(),
      );

      LoggingService.info(
        'تم حذف الوثيقة',
        category: 'DocumentService',
        data: {'documentId': documentId, 'documentName': document.documentName},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الوثيقة',
        category: 'DocumentService',
        data: {'error': e.toString(), 'documentId': documentId},
      );
      rethrow;
    }
  }

  /// قراءة محتوى وثيقة
  Future<Uint8List?> readDocument(int documentId) async {
    try {
      final document = await getDocumentById(documentId);
      if (document == null) {
        throw ValidationException('الوثيقة غير موجودة');
      }

      final file = File(document.filePath);
      if (!await file.exists()) {
        throw ValidationException('ملف الوثيقة غير موجود');
      }

      return await file.readAsBytes();
    } catch (e) {
      LoggingService.error(
        'خطأ في قراءة الوثيقة',
        category: 'DocumentService',
        data: {'error': e.toString(), 'documentId': documentId},
      );
      return null;
    }
  }

  /// الحصول على الوثائق المنتهية الصلاحية
  Future<List<Document>> getExpiredDocuments({
    int? employeeId,
    int? daysBeforeExpiry = 0,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final targetDate = DateTime.now().add(
        Duration(days: daysBeforeExpiry ?? 0),
      );

      String whereClause = 'expiry_date IS NOT NULL AND expiry_date <= ?';
      List<dynamic> whereArgs = [targetDate.toIso8601String().split('T')[0]];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      final result = await db.query(
        AppConstants.documentsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'expiry_date ASC',
      );

      return result.map((map) => Document.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الوثائق المنتهية الصلاحية',
        category: 'DocumentService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// البحث في الوثائق
  Future<List<Document>> searchDocuments({
    required String searchQuery,
    int? employeeId,
    String? documentType,
    bool? isConfidential,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '(document_name LIKE ? OR description LIKE ?)';
      List<dynamic> whereArgs = ['%$searchQuery%', '%$searchQuery%'];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (documentType != null && documentType.isNotEmpty) {
        whereClause += ' AND document_type = ?';
        whereArgs.add(documentType);
      }

      if (isConfidential != null) {
        whereClause += ' AND is_confidential = ?';
        whereArgs.add(isConfidential ? 1 : 0);
      }

      final result = await db.query(
        AppConstants.documentsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );

      return result.map((map) => Document.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في الوثائق',
        category: 'DocumentService',
        data: {'error': e.toString(), 'searchQuery': searchQuery},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات الوثائق
  Future<Map<String, dynamic>> getDocumentStatistics({
    int? employeeId,
  }) async {
    try {
      final documents = employeeId != null
          ? await getEmployeeDocuments(employeeId: employeeId)
          : <Document>[];

      final stats = <String, dynamic>{
        'total': documents.length,
        'byType': <String, int>{},
        'confidential': 0,
        'expired': 0,
        'expiringSoon': 0,
        'totalSize': 0,
      };

      for (final document in documents) {
        // إحصائيات حسب النوع
        final type = document.documentType;
        stats['byType'][type] = (stats['byType'][type] ?? 0) + 1;

        // الوثائق السرية
        if (document.isConfidential) {
          stats['confidential']++;
        }

        // الوثائق المنتهية الصلاحية
        if (document.isExpired) {
          stats['expired']++;
        }

        // الوثائق قريبة الانتهاء
        if (document.isExpiringSoon) {
          stats['expiringSoon']++;
        }

        // الحجم الإجمالي
        stats['totalSize'] += document.fileSize;
      }

      return stats;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات الوثائق',
        category: 'DocumentService',
        data: {'error': e.toString()},
      );
      return {
        'total': 0,
        'byType': <String, int>{},
        'confidential': 0,
        'expired': 0,
        'expiringSoon': 0,
        'totalSize': 0,
      };
    }
  }

  /// دوال مساعدة

  /// إنشاء مجلد الوثائق
  Future<Directory> _createDocumentsDirectory(int employeeId) async {
    final appDir = Directory.current;
    final documentsPath = path.join(
      appDir.path,
      _documentsFolder,
      _employeeDocumentsFolder,
      'employee_$employeeId',
    );

    final directory = Directory(documentsPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    return directory;
  }

  /// إنشاء اسم ملف فريد
  String _generateUniqueFileName(String originalName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(originalName);
    final nameWithoutExtension = path.basenameWithoutExtension(originalName);

    return '${nameWithoutExtension}_$timestamp$extension';
  }

  /// التحقق من صحة البيانات
  Future<void> _validateDocument({
    required int employeeId,
    required String documentType,
    required String documentName,
    required Uint8List fileData,
  }) async {
    if (documentName.trim().isEmpty) {
      throw ValidationException('اسم الوثيقة مطلوب');
    }

    if (fileData.isEmpty) {
      throw ValidationException('بيانات الملف مطلوبة');
    }

    // التحقق من حجم الملف (حد أقصى 10 ميجابايت)
    const maxFileSize = 10 * 1024 * 1024; // 10 MB
    if (fileData.length > maxFileSize) {
      throw ValidationException(
        'حجم الملف كبير جداً (الحد الأقصى 10 ميجابايت)',
      );
    }

    // التحقق من نوع الوثيقة
    const allowedTypes = [
      'contract',
      'id_copy',
      'certificate',
      'photo',
      'cv',
      'medical',
      'other',
    ];

    if (!allowedTypes.contains(documentType)) {
      throw ValidationException('نوع الوثيقة غير مدعوم');
    }

    // التحقق من وجود الموظف
    final db = await _databaseHelper.database;
    final employeeResult = await db.query(
      AppConstants.employeesTable,
      where: 'id = ?',
      whereArgs: [employeeId],
    );

    if (employeeResult.isEmpty) {
      throw ValidationException('الموظف غير موجود');
    }
  }

  /// الحصول على أنواع الوثائق المدعومة
  static List<Map<String, String>> getSupportedDocumentTypes() {
    return [
      {'value': 'contract', 'label': 'عقد'},
      {'value': 'id_copy', 'label': 'صورة هوية'},
      {'value': 'certificate', 'label': 'شهادة'},
      {'value': 'photo', 'label': 'صورة شخصية'},
      {'value': 'cv', 'label': 'سيرة ذاتية'},
      {'value': 'medical', 'label': 'تقرير طبي'},
      {'value': 'other', 'label': 'أخرى'},
    ];
  }

  /// التحقق من امتداد الملف المدعوم
  static bool isSupportedFileType(String fileName) {
    const supportedExtensions = [
      '.pdf',
      '.doc',
      '.docx',
      '.txt',
      '.rtf',
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.xls',
      '.xlsx',
      '.csv',
    ];

    final extension = path.extension(fileName).toLowerCase();
    return supportedExtensions.contains(extension);
  }

  /// الحصول على أيقونة حسب نوع الملف
  static String getFileIcon(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    switch (extension) {
      case '.pdf':
        return 'pdf';
      case '.doc':
      case '.docx':
        return 'word';
      case '.xls':
      case '.xlsx':
        return 'excel';
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.bmp':
        return 'image';
      default:
        return 'file';
    }
  }
}
