/// خدمة إدارة الموافقات
/// توفر وظائف شاملة لإدارة نظام الموافقات على الطلبات المختلفة
library;

import 'dart:convert';
import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

class ApprovalService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع الموافقات
  Future<List<Approval>> getAllApprovals({
    int? employeeId,
    int? approverId,
    String? status,
    String? requestType,
    DateTime? fromDate,
    DateTime? toDate,
    String? priority,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (approverId != null) {
        whereClause += ' AND approver_id = ?';
        whereArgs.add(approverId);
      }

      if (status != null && status.isNotEmpty) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (requestType != null && requestType.isNotEmpty) {
        whereClause += ' AND request_type = ?';
        whereArgs.add(requestType);
      }

      if (priority != null && priority.isNotEmpty) {
        whereClause += ' AND priority = ?';
        whereArgs.add(priority);
      }

      if (fromDate != null) {
        whereClause += ' AND requested_at >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND requested_at <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final result = await db.query(
        AppConstants.approvalsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'requested_at DESC',
      );

      return result.map((map) => Approval.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الموافقات',
        category: 'ApprovalService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على موافقة بالمعرف
  Future<Approval?> getApprovalById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.approvalsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return Approval.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب الموافقة',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'id': id},
      );
      return null;
    }
  }

  /// إنشاء طلب موافقة جديد
  Future<Approval> createApproval({
    required String requestType,
    required int requestId,
    required int employeeId,
    int? approverId,
    String priority = 'normal',
    required String title,
    String? description,
    Map<String, dynamic>? requestData,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateApproval(
        requestType: requestType,
        employeeId: employeeId,
        title: title,
        priority: priority,
      );

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final approval = Approval(
        requestType: requestType,
        requestId: requestId,
        employeeId: employeeId,
        approverId: approverId,
        priority: priority,
        title: title,
        description: description,
        requestData: requestData != null ? jsonEncode(requestData) : null,
        requestedAt: now,
        createdAt: now,
        updatedAt: now,
      );

      final id = await db.insert(AppConstants.approvalsTable, approval.toMap());

      final newApproval = approval.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Approval',
        entityId: id,
        description: 'إنشاء طلب موافقة جديد: $title',
        newValues: newApproval.toMap(),
      );

      LoggingService.info(
        'تم إنشاء طلب موافقة جديد',
        category: 'ApprovalService',
        data: {
          'approvalId': id,
          'requestType': requestType,
          'employeeId': employeeId,
        },
      );

      return newApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء طلب الموافقة',
        category: 'ApprovalService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الموافقة على طلب
  Future<Approval> approveRequest({
    required int approvalId,
    required int approverId,
    String? approvalNotes,
  }) async {
    try {
      final approval = await getApprovalById(approvalId);
      if (approval == null) {
        throw ValidationException('طلب الموافقة غير موجود');
      }

      if (approval.status != 'pending') {
        throw ValidationException(
          'لا يمكن الموافقة على طلب تم التعامل معه مسبقاً',
        );
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedApproval = approval.copyWith(
        status: 'approved',
        approverId: approverId,
        approvalNotes: approvalNotes,
        approvedAt: now,
        updatedAt: now,
      );

      await db.update(
        AppConstants.approvalsTable,
        updatedApproval.toMap(),
        where: 'id = ?',
        whereArgs: [approvalId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'APPROVE',
        entityType: 'Approval',
        entityId: approvalId,
        description: 'الموافقة على الطلب: ${approval.title}',
        oldValues: approval.toMap(),
        newValues: updatedApproval.toMap(),
      );

      LoggingService.info(
        'تم الموافقة على الطلب',
        category: 'ApprovalService',
        data: {
          'approvalId': approvalId,
          'approverId': approverId,
          'requestType': approval.requestType,
        },
      );

      return updatedApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في الموافقة على الطلب',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'approvalId': approvalId},
      );
      rethrow;
    }
  }

  /// رفض طلب
  Future<Approval> rejectRequest({
    required int approvalId,
    required int approverId,
    required String rejectionReason,
  }) async {
    try {
      final approval = await getApprovalById(approvalId);
      if (approval == null) {
        throw ValidationException('طلب الموافقة غير موجود');
      }

      if (approval.status != 'pending') {
        throw ValidationException('لا يمكن رفض طلب تم التعامل معه مسبقاً');
      }

      if (rejectionReason.trim().isEmpty) {
        throw ValidationException('سبب الرفض مطلوب');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedApproval = approval.copyWith(
        status: 'rejected',
        approverId: approverId,
        rejectionReason: rejectionReason,
        approvedAt: now,
        updatedAt: now,
      );

      await db.update(
        AppConstants.approvalsTable,
        updatedApproval.toMap(),
        where: 'id = ?',
        whereArgs: [approvalId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'REJECT',
        entityType: 'Approval',
        entityId: approvalId,
        description: 'رفض الطلب: ${approval.title}',
        oldValues: approval.toMap(),
        newValues: updatedApproval.toMap(),
      );

      LoggingService.info(
        'تم رفض الطلب',
        category: 'ApprovalService',
        data: {
          'approvalId': approvalId,
          'approverId': approverId,
          'requestType': approval.requestType,
        },
      );

      return updatedApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفض الطلب',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'approvalId': approvalId},
      );
      rethrow;
    }
  }

  /// إلغاء طلب
  Future<Approval> cancelRequest({
    required int approvalId,
    required int employeeId,
  }) async {
    try {
      final approval = await getApprovalById(approvalId);
      if (approval == null) {
        throw ValidationException('طلب الموافقة غير موجود');
      }

      if (approval.employeeId != employeeId) {
        throw ValidationException('لا يمكن إلغاء طلب موافقة لموظف آخر');
      }

      if (approval.status != 'pending') {
        throw ValidationException('لا يمكن إلغاء طلب تم التعامل معه مسبقاً');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();

      final updatedApproval = approval.copyWith(
        status: 'cancelled',
        updatedAt: now,
      );

      await db.update(
        AppConstants.approvalsTable,
        updatedApproval.toMap(),
        where: 'id = ?',
        whereArgs: [approvalId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CANCEL',
        entityType: 'Approval',
        entityId: approvalId,
        description: 'إلغاء الطلب: ${approval.title}',
        oldValues: approval.toMap(),
        newValues: updatedApproval.toMap(),
      );

      LoggingService.info(
        'تم إلغاء الطلب',
        category: 'ApprovalService',
        data: {
          'approvalId': approvalId,
          'employeeId': employeeId,
          'requestType': approval.requestType,
        },
      );

      return updatedApproval;
    } catch (e) {
      LoggingService.error(
        'خطأ في إلغاء الطلب',
        category: 'ApprovalService',
        data: {'error': e.toString(), 'approvalId': approvalId},
      );
      rethrow;
    }
  }

  /// الحصول على الطلبات المعلقة للموافقة
  Future<List<Approval>> getPendingApprovals({
    int? approverId,
    String? requestType,
    String? priority,
  }) async {
    return getAllApprovals(
      approverId: approverId,
      status: 'pending',
      requestType: requestType,
      priority: priority,
    );
  }

  /// الحصول على طلبات موظف معين
  Future<List<Approval>> getEmployeeApprovals({
    required int employeeId,
    String? status,
    String? requestType,
  }) async {
    return getAllApprovals(
      employeeId: employeeId,
      status: status,
      requestType: requestType,
    );
  }

  /// دوال التحقق من صحة البيانات

  Future<void> _validateApproval({
    required String requestType,
    required int employeeId,
    required String title,
    required String priority,
  }) async {
    if (title.trim().isEmpty) {
      throw ValidationException('عنوان الطلب مطلوب');
    }

    if (![
      'leave',
      'loan',
      'overtime',
      'expense',
      'training',
      'other',
    ].contains(requestType)) {
      throw ValidationException('نوع الطلب غير صحيح');
    }

    if (!['low', 'normal', 'high', 'urgent'].contains(priority)) {
      throw ValidationException('أولوية الطلب غير صحيحة');
    }

    // التحقق من وجود الموظف
    final db = await _databaseHelper.database;
    final employeeResult = await db.query(
      AppConstants.employeesTable,
      where: 'id = ?',
      whereArgs: [employeeId],
    );

    if (employeeResult.isEmpty) {
      throw ValidationException('الموظف غير موجود');
    }
  }

  /// إنشاء طلب موافقة إجازة
  Future<Approval> createLeaveApproval({
    required int leaveId,
    required int employeeId,
    required String leaveType,
    required DateTime startDate,
    required DateTime endDate,
    required int totalDays,
    String? reason,
    int? approverId,
  }) async {
    final requestData = {
      'leave_type': leaveType,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_days': totalDays,
      'reason': reason,
    };

    return createApproval(
      requestType: 'leave',
      requestId: leaveId,
      employeeId: employeeId,
      approverId: approverId,
      title: 'طلب إجازة $leaveType',
      description:
          'طلب إجازة من ${startDate.day}/${startDate.month}/${startDate.year} إلى ${endDate.day}/${endDate.month}/${endDate.year}',
      requestData: requestData,
    );
  }

  /// إنشاء طلب موافقة قرض
  Future<Approval> createLoanApproval({
    required int loanId,
    required int employeeId,
    required double amount,
    required int installments,
    String? purpose,
    int? approverId,
  }) async {
    final requestData = {
      'amount': amount,
      'installments': installments,
      'purpose': purpose,
    };

    return createApproval(
      requestType: 'loan',
      requestId: loanId,
      employeeId: employeeId,
      approverId: approverId,
      priority: amount > 1000000
          ? 'high'
          : 'normal', // أولوية عالية للمبالغ الكبيرة
      title: 'طلب قرض بمبلغ ${amount.toStringAsFixed(0)} ل.س',
      description:
          'طلب قرض بمبلغ ${amount.toStringAsFixed(0)} ل.س على ${installments} قسط',
      requestData: requestData,
    );
  }
}
