/// شاشة التقارير المتقدمة لنظام الموارد البشرية
/// تحتوي على جميع أنواع التقارير والتحليلات المطلوبة
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/employee_service.dart';
import '../services/attendance_service.dart';
import '../services/payroll_service.dart';
import '../services/leave_service.dart';
import '../services/loan_service.dart';
import '../services/performance_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';

class HRReportsScreen extends StatefulWidget {
  const HRReportsScreen({super.key});

  @override
  State<HRReportsScreen> createState() => _HRReportsScreenState();
}

class _HRReportsScreenState extends State<HRReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // الخدمات
  final EmployeeService _employeeService = EmployeeService();
  final AttendanceService _attendanceService = AttendanceService();
  final PayrollService _payrollService = PayrollService();
  final LeaveService _leaveService = LeaveService();
  final LoanService _loanService = LoanService();
  final PerformanceService _performanceService = PerformanceService();

  // البيانات
  List<Employee> _employees = [];
  Map<String, dynamic> _employeeStats = {};
  Map<String, dynamic> _attendanceStats = {};
  Map<String, dynamic> _payrollStats = {};
  Map<String, dynamic> _leaveStats = {};
  Map<String, dynamic> _loanStats = {};
  Map<String, dynamic> _performanceStats = {};

  bool _isLoading = true;
  String? _error;

  // فلاتر التقارير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int? _selectedEmployeeId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل الموظفين
      final employees = await _employeeService.getAllEmployees();

      // تحميل الإحصائيات (محاكاة البيانات)
      final employeeStats = _generateEmployeeStats(employees);
      final attendanceStats = _generateAttendanceStats();
      final payrollStats = _generatePayrollStats();
      final leaveStats = _generateLeaveStats();
      final loanStats = _generateLoanStats();
      final performanceStats = _generatePerformanceStats();

      setState(() {
        _employees = employees;
        _employeeStats = employeeStats;
        _attendanceStats = attendanceStats;
        _payrollStats = payrollStats;
        _leaveStats = leaveStats;
        _loanStats = loanStats;
        _performanceStats = performanceStats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Map<String, dynamic> _generateEmployeeStats(List<Employee> employees) {
    return {
      'total': employees.length,
      'active': employees.where((e) => e.isActive).length,
      'inactive': employees.where((e) => !e.isActive).length,
      'newHires': 5, // محاكاة
      'averageAge': 32.5,
      'byGender': {
        'male': employees.length * 0.6,
        'female': employees.length * 0.4,
      },
    };
  }

  Map<String, dynamic> _generateAttendanceStats() {
    return {
      'totalRecords': 150,
      'presentDays': 140,
      'absentDays': 10,
      'lateDays': 15,
      'overtimeHours': 45.5,
      'averageWorkingHours': 8.2,
      'attendanceRate': 93.3,
    };
  }

  Map<String, dynamic> _generatePayrollStats() {
    return {
      'totalPayrolls': 25,
      'totalGrossSalary': *********.0,
      'totalNetSalary': *********.0,
      'totalDeductions': 25000000.0,
      'totalAllowances': 15000000.0,
      'totalTax': 10000000.0,
      'averageGrossSalary': 5000000.0,
      'averageNetSalary': 4000000.0,
    };
  }

  Map<String, dynamic> _generateLeaveStats() {
    return {
      'totalLeaves': 35,
      'approvedLeaves': 28,
      'pendingLeaves': 5,
      'rejectedLeaves': 2,
      'totalDays': 180,
      'averageDuration': 5.1,
      'byType': {'annual': 20, 'sick': 10, 'emergency': 5},
    };
  }

  Map<String, dynamic> _generateLoanStats() {
    return {
      'totalLoans': 12,
      'activeLoans': 8,
      'completedLoans': 4,
      'totalAmount': 60000000.0,
      'totalPaid': 25000000.0,
      'totalRemaining': 35000000.0,
      'averageLoanAmount': 5000000.0,
    };
  }

  Map<String, dynamic> _generatePerformanceStats() {
    return {
      'totalEvaluations': 20,
      'averageScore': 82.5,
      'excellentCount': 5,
      'goodCount': 10,
      'averageCount': 4,
      'poorCount': 1,
      'improvementNeeded': 0,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المتقدمة'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(text: 'الموظفين', icon: Icon(Icons.people)),
            Tab(text: 'الحضور', icon: Icon(Icons.access_time)),
            Tab(text: 'الرواتب', icon: Icon(Icons.account_balance_wallet)),
            Tab(text: 'الإجازات', icon: Icon(Icons.event_available)),
            Tab(text: 'القروض', icon: Icon(Icons.money)),
            Tab(text: 'الأداء', icon: Icon(Icons.trending_up)),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: _isLoading
                ? const LoadingWidget()
                : _error != null
                ? _buildErrorWidget()
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildEmployeeReport(),
                      _buildAttendanceReport(),
                      _buildPayrollReport(),
                      _buildLeaveReport(),
                      _buildLoanReport(),
                      _buildPerformanceReport(),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _exportReports,
        backgroundColor: RevolutionaryColors.successGlow,
        child: const Icon(Icons.file_download),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ في تحميل التقارير',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(_error ?? 'خطأ غير معروف'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _selectDate(true),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, size: 20),
                        const SizedBox(width: 8),
                        Text('من: ${_formatDate(_startDate)}'),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: InkWell(
                  onTap: () => _selectDate(false),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, size: 20),
                        const SizedBox(width: 8),
                        Text('إلى: ${_formatDate(_endDate)}'),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<int>(
                  value: _selectedEmployeeId,
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع الموظفين'),
                    ),
                    ..._employees.map((employee) {
                      return DropdownMenuItem(
                        value: employee.id,
                        child: Text(
                          '${employee.firstName} ${employee.lastName}',
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedEmployeeId = value);
                    _loadData();
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.damascusSky,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeReport() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تقرير الموظفين',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // إحصائيات عامة
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الموظفين',
                  _employeeStats['total']?.toString() ?? '0',
                  RevolutionaryColors.damascusSky,
                  Icons.people,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الموظفين النشطين',
                  _employeeStats['active']?.toString() ?? '0',
                  RevolutionaryColors.successGlow,
                  Icons.check_circle,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'الموظفين غير النشطين',
                  _employeeStats['inactive']?.toString() ?? '0',
                  RevolutionaryColors.errorCoral,
                  Icons.person_off,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'التوظيفات الجديدة',
                  _employeeStats['newHires']?.toString() ?? '0',
                  RevolutionaryColors.warningAmber,
                  Icons.person_add,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // معلومات إضافية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات إضافية',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow(
                    'متوسط العمر',
                    '${(_employeeStats['averageAge'] ?? 0).toStringAsFixed(1)} سنة',
                  ),
                  _buildInfoRow(
                    'نسبة الذكور',
                    '${((_employeeStats['byGender']?['male'] ?? 0) / (_employeeStats['total'] ?? 1) * 100).toStringAsFixed(1)}%',
                  ),
                  _buildInfoRow(
                    'نسبة الإناث',
                    '${((_employeeStats['byGender']?['female'] ?? 0) / (_employeeStats['total'] ?? 1) * 100).toStringAsFixed(1)}%',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceReport() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تقرير الحضور والانصراف',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'أيام الحضور',
                  _attendanceStats['presentDays']?.toString() ?? '0',
                  RevolutionaryColors.successGlow,
                  Icons.check_circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'أيام الغياب',
                  _attendanceStats['absentDays']?.toString() ?? '0',
                  RevolutionaryColors.errorCoral,
                  Icons.cancel,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'أيام التأخير',
                  _attendanceStats['lateDays']?.toString() ?? '0',
                  RevolutionaryColors.warningAmber,
                  Icons.schedule,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'نسبة الحضور',
                  '${(_attendanceStats['attendanceRate'] ?? 0).toStringAsFixed(1)}%',
                  RevolutionaryColors.damascusSky,
                  Icons.trending_up,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل الحضور',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow(
                    'متوسط ساعات العمل',
                    '${(_attendanceStats['averageWorkingHours'] ?? 0).toStringAsFixed(1)} ساعة',
                  ),
                  _buildInfoRow(
                    'الساعات الإضافية',
                    '${(_attendanceStats['overtimeHours'] ?? 0).toStringAsFixed(1)} ساعة',
                  ),
                  _buildInfoRow(
                    'إجمالي السجلات',
                    '${_attendanceStats['totalRecords'] ?? 0}',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayrollReport() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تقرير الرواتب',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الراتب الإجمالي',
                  _formatCurrency(_payrollStats['totalGrossSalary'] ?? 0),
                  RevolutionaryColors.damascusSky,
                  Icons.account_balance_wallet,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'إجمالي الراتب الصافي',
                  _formatCurrency(_payrollStats['totalNetSalary'] ?? 0),
                  RevolutionaryColors.successGlow,
                  Icons.money,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الاستقطاعات',
                  _formatCurrency(_payrollStats['totalDeductions'] ?? 0),
                  RevolutionaryColors.errorCoral,
                  Icons.remove_circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'إجمالي البدلات',
                  _formatCurrency(_payrollStats['totalAllowances'] ?? 0),
                  RevolutionaryColors.warningAmber,
                  Icons.add_circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLeaveReport() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تقرير الإجازات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الإجازات',
                  _leaveStats['totalLeaves']?.toString() ?? '0',
                  RevolutionaryColors.damascusSky,
                  Icons.event_available,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الإجازات المعتمدة',
                  _leaveStats['approvedLeaves']?.toString() ?? '0',
                  RevolutionaryColors.successGlow,
                  Icons.check_circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoanReport() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تقرير القروض',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي القروض',
                  _loanStats['totalLoans']?.toString() ?? '0',
                  RevolutionaryColors.damascusSky,
                  Icons.money,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'القروض النشطة',
                  _loanStats['activeLoans']?.toString() ?? '0',
                  RevolutionaryColors.successGlow,
                  Icons.trending_up,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceReport() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تقرير الأداء',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي التقييمات',
                  _performanceStats['totalEvaluations']?.toString() ?? '0',
                  RevolutionaryColors.damascusSky,
                  Icons.assessment,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'متوسط النقاط',
                  '${(_performanceStats['averageScore'] ?? 0).toStringAsFixed(1)}',
                  RevolutionaryColors.successGlow,
                  Icons.star,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // الدوال المساعدة

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} ل.س';
  }

  Future<void> _selectDate(bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
        } else {
          _endDate = date;
        }
      });
      _loadData();
    }
  }

  void _exportReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير التقارير - قيد التطوير'),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }
}
