/// شاشة التخطيط الوظيفي
/// توفر واجهة شاملة لإدارة التخطيط الوظيفي والتطوير المهني
library;

import 'package:flutter/material.dart';

import '../models/hr_models.dart';
import '../services/career_planning_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/revolutionary_design_colors.dart';

class CareerPlanningScreen extends StatefulWidget {
  const CareerPlanningScreen({super.key});

  @override
  State<CareerPlanningScreen> createState() => _CareerPlanningScreenState();
}

class _CareerPlanningScreenState extends State<CareerPlanningScreen>
    with TickerProviderStateMixin {
  final CareerPlanningService _careerService = CareerPlanningService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<CareerPath> _careerPaths = [];
  List<CareerPath> _filteredCareerPaths = [];
  List<CareerDevelopmentPlan> _developmentPlans = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _careerService.getAllCareerPaths(),
        _careerService.getDevelopmentPlans(),
        _careerService.getCareerReviews(),
        _careerService.getCareerPlanningStatistics(),
        _employeeService.getAllEmployees(),
      ]);

      setState(() {
        _careerPaths = results[0] as List<CareerPath>;
        _filteredCareerPaths = List.from(_careerPaths);
        _developmentPlans = results[1] as List<CareerDevelopmentPlan>;
        _statistics = results[3] as Map<String, dynamic>;
        _employees = results[4] as List<Employee>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات التخطيط الوظيفي',
        category: 'CareerPlanningScreen',
        data: {'error': e.toString()},
      );
    }
  }

  /// تطبيق البحث في المسارات الوظيفية
  void _filterCareerPaths(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredCareerPaths = List.from(_careerPaths);
      } else {
        _filteredCareerPaths = _careerPaths.where((path) {
          final searchLower = query.toLowerCase();
          return path.pathName.toLowerCase().contains(searchLower) ||
              (path.description?.toLowerCase().contains(searchLower) ??
                  false) ||
              path.requirements.any(
                (requirement) =>
                    requirement.toLowerCase().contains(searchLower),
              );
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التخطيط الوظيفي'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'add_path':
                  _showAddCareerPathDialog();
                  break;
                case 'create_plan':
                  _showCreateDevelopmentPlanDialog();
                  break;
                case 'create_defaults':
                  _createDefaultCareerPaths();
                  break;
                case 'export':
                  _exportCareerData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_path',
                child: Text('إضافة مسار وظيفي'),
              ),
              const PopupMenuItem(
                value: 'create_plan',
                child: Text('إنشاء خطة تطوير'),
              ),
              const PopupMenuItem(
                value: 'create_defaults',
                child: Text('إنشاء المسارات الافتراضية'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.route), text: 'المسارات الوظيفية'),
            Tab(icon: Icon(Icons.trending_up), text: 'خطط التطوير'),
            Tab(icon: Icon(Icons.rate_review), text: 'المراجعات'),
            Tab(icon: Icon(Icons.analytics), text: 'التقارير'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildCareerPathsTab(),
        _buildDevelopmentPlansTab(),
        _buildReviewsTab(),
        _buildReportsTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentPlans(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات التخطيط الوظيفي',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'المسارات الوظيفية',
              '${_statistics['totalPaths'] ?? 0}',
              Icons.route,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'خطط التطوير النشطة',
              '${_statistics['activePlans'] ?? 0}',
              Icons.trending_up,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'معدل الإكمال',
              '${_statistics['completionRate'] ?? '0.0'}%',
              Icons.check_circle,
              RevolutionaryColors.infoTurquoise,
            ),
            _buildStatCard(
              'متوسط التقدم',
              '${(_statistics['averageProgress'] as double? ?? 0.0).toStringAsFixed(1)}%',
              Icons.show_chart,
              RevolutionaryColors.warningAmber,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إضافة مسار وظيفي',
              Icons.add_road,
              RevolutionaryColors.successGlow,
              _showAddCareerPathDialog,
            ),
            _buildActionCard(
              'إنشاء خطة تطوير',
              Icons.trending_up,
              RevolutionaryColors.infoTurquoise,
              _showCreateDevelopmentPlanDialog,
            ),
            _buildActionCard(
              'إضافة مراجعة',
              Icons.rate_review,
              RevolutionaryColors.warningAmber,
              _showAddReviewDialog,
            ),
            _buildActionCard(
              'تقرير التطوير',
              Icons.analytics,
              RevolutionaryColors.damascusSky,
              _generateDevelopmentReport,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentPlans() {
    final recentPlans = _developmentPlans.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خطط التطوير الحديثة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (recentPlans.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد خطط تطوير',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...recentPlans.map((plan) {
            final employee = _employees.firstWhere(
              (emp) => emp.id == plan.employeeId,
              orElse: () => Employee(
                employeeNumber: 'غير معروف',
                nationalId: '',
                firstName: 'غير معروف',
                lastName: '',
                fullName: 'غير معروف',
                email: '',
                phone: '',
                hireDate: DateTime.now(),
                basicSalary: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withValues(
                    alpha: 0.1,
                  ),
                  child: Text(
                    employee.firstName.isNotEmpty ? employee.firstName[0] : '؟',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text('${employee.firstName} ${employee.lastName}'),
                subtitle: Text(
                  plan.goals.length > 50
                      ? '${plan.goals.substring(0, 50)}...'
                      : plan.goals,
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${plan.progressPercentage.toStringAsFixed(0)}%',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: RevolutionaryColors.successGlow,
                      ),
                    ),
                    Text(
                      plan.statusText,
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(plan.status),
                      ),
                    ),
                  ],
                ),
                onTap: () => _showPlanDetails(plan, employee),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildCareerPathsTab() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'البحث في المسارات الوظيفية...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: _filterCareerPaths,
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: _showAddCareerPathDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة مسار'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: RevolutionaryColors.successGlow,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        // قائمة المسارات
        Expanded(
          child: _filteredCareerPaths.isEmpty
              ? Center(
                  child: Text(
                    _searchQuery.isEmpty
                        ? 'لا توجد مسارات وظيفية'
                        : 'لا توجد نتائج للبحث "$_searchQuery"',
                    style: const TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _filteredCareerPaths.length,
                  itemBuilder: (context, index) {
                    final path = _filteredCareerPaths[index];
                    return _buildCareerPathCard(path);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildCareerPathCard(CareerPath path) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Icon(
          _getPathIcon(path.level),
          color: _getLevelColor(path.level),
        ),
        title: Text(
          path.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('${path.department} - ${path.levelText}'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(path.description, style: const TextStyle(fontSize: 14)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildPathDetail('المستوى', path.levelText),
                    ),
                    Expanded(
                      child: _buildPathDetail(
                        'سنوات الخبرة',
                        '${path.experienceYears}',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildPathDetail('نطاق الراتب', path.salaryRange),
                    ),
                    Expanded(
                      child: _buildPathDetail(
                        'المستوى التالي',
                        path.nextLevelPath ?? 'غير محدد',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (path.requiredSkills.isNotEmpty) ...[
                  const Text(
                    'المهارات المطلوبة:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: path.requiredSkills
                        .map(
                          (skill) => Chip(
                            label: Text(
                              skill,
                              style: const TextStyle(fontSize: 12),
                            ),
                            backgroundColor: RevolutionaryColors.infoTurquoise
                                .withValues(alpha: 0.1),
                          ),
                        )
                        .toList(),
                  ),
                  const SizedBox(height: 12),
                ],
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _suggestEmployeesForPath(path),
                      icon: const Icon(Icons.people),
                      label: const Text('اقتراح موظفين'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.infoTurquoise,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: () => _editCareerPath(path),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPathDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildDevelopmentPlansTab() {
    return const Center(child: Text('تبويب خطط التطوير - قيد التطوير'));
  }

  Widget _buildReviewsTab() {
    return const Center(child: Text('تبويب المراجعات - قيد التطوير'));
  }

  Widget _buildReportsTab() {
    return const Center(child: Text('تبويب التقارير - قيد التطوير'));
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // المسارات الوظيفية
        return FloatingActionButton(
          onPressed: _showAddCareerPathDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 2: // خطط التطوير
        return FloatingActionButton(
          onPressed: _showCreateDevelopmentPlanDialog,
          backgroundColor: RevolutionaryColors.infoTurquoise,
          child: const Icon(Icons.trending_up, color: Colors.white),
        );
      case 3: // المراجعات
        return FloatingActionButton(
          onPressed: _showAddReviewDialog,
          backgroundColor: RevolutionaryColors.warningAmber,
          child: const Icon(Icons.rate_review, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال مساعدة
  IconData _getPathIcon(String level) {
    switch (level) {
      case 'entry':
        return Icons.school;
      case 'junior':
        return Icons.trending_up;
      case 'mid':
        return Icons.work;
      case 'senior':
        return Icons.star;
      case 'lead':
        return Icons.group;
      case 'manager':
        return Icons.supervisor_account;
      case 'director':
        return Icons.business_center;
      default:
        return Icons.work;
    }
  }

  Color _getLevelColor(String level) {
    switch (level) {
      case 'entry':
        return RevolutionaryColors.successGlow;
      case 'junior':
        return RevolutionaryColors.infoTurquoise;
      case 'mid':
        return RevolutionaryColors.warningAmber;
      case 'senior':
        return RevolutionaryColors.damascusSky;
      case 'lead':
        return RevolutionaryColors.errorCoral;
      case 'manager':
        return Colors.purple;
      case 'director':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return RevolutionaryColors.successGlow;
      case 'completed':
        return RevolutionaryColors.infoTurquoise;
      case 'cancelled':
        return RevolutionaryColors.errorCoral;
      case 'on_hold':
        return RevolutionaryColors.warningAmber;
      default:
        return Colors.grey;
    }
  }

  // دوال الأحداث
  void _showAddCareerPathDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة مسار وظيفي - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showCreateDevelopmentPlanDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إنشاء خطة تطوير - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showAddReviewDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة مراجعة - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _createDefaultCareerPaths() async {
    try {
      await _careerService.createDefaultCareerPaths();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء المسارات الوظيفية الافتراضية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المسارات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _suggestEmployeesForPath(CareerPath path) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('اقتراح موظفين للمسار: ${path.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _editCareerPath(CareerPath path) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تعديل المسار: ${path.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _generateDevelopmentReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث تقرير التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
    _loadData();
  }

  void _exportCareerData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير بيانات التخطيط الوظيفي - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showPlanDetails(CareerDevelopmentPlan plan, Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('خطة تطوير ${employee.firstName} ${employee.lastName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('الحالة', plan.statusText),
              _buildDetailRow(
                'التقدم',
                '${plan.progressPercentage.toStringAsFixed(1)}%',
              ),
              _buildDetailRow(
                'تاريخ البداية',
                '${plan.startDate.day}/${plan.startDate.month}/${plan.startDate.year}',
              ),
              _buildDetailRow(
                'التاريخ المستهدف',
                '${plan.targetDate.day}/${plan.targetDate.month}/${plan.targetDate.year}',
              ),
              if (plan.goals.isNotEmpty) ...[
                const Divider(),
                const Text(
                  'الأهداف:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(plan.goals),
              ],
              if (plan.skillGaps.isNotEmpty) ...[
                const Divider(),
                const Text(
                  'فجوات المهارات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(plan.skillGaps),
              ],
              if (plan.developmentActions.isNotEmpty) ...[
                const Divider(),
                const Text(
                  'إجراءات التطوير:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(plan.developmentActions),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
